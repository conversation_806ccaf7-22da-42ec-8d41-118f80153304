//
//  SellingMultipleCarsScreenView.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 30/05/2025.
//  Copyright © 2025 <PERSON><PERSON>. All rights reserved.
//

import SwiftUI

struct SellingMultipleCarsScreenView: View {
    @ObservedObject var viewModel: MicroDealerViewModel

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                ScrollView {
                    VStack(spacing: 0) {
                        NotificationBanner {
                            viewModel.getContact()
                        }
                        
                        PlansSection(
                            selectedPackageBundle: $viewModel.selectedPackageBundle,
                            packageBundles: viewModel.packageBundles
                        )
                        .padding(.bottom, 32)
                        
//                        SingleCarSection {
//                            viewModel.viewSingleDeals()
//                        }
                        
                        HelpSection {
                            viewModel.getHelp()
                        }
                    }
                }
                
                StickyContinueButton {
                    viewModel.onContinue()
                }
            }
            .navigationTitle("Sell multiple cars")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .background(Color(.systemGray6))
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: viewModel.goBack) {
                        Image(systemName: "chevron.left")
                            .foregroundStyle(Color(uiColor: Colors.charcoalColor))
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel", action: viewModel.goBack)
                        .foregroundStyle(Color(uiColor: Colors.bluishColor))
                }
            }
            .onAppear {
                self.viewModel.getPackageBundlesDetails(isBundle: true, includeSelfService: true)
            }
        }
    }
}
