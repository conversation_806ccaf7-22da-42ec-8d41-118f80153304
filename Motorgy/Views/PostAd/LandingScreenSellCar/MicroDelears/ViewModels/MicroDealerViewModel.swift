//
//  MicroDealerViewModel.swift
//  Motorgy
//
//  Created by <PERSON> on 03/06/2025.
//  Copyright <PERSON>. All rights reserved.
//

import Foundation
import SwiftUI

class MicroDealerViewModel: ObservableObject {
    @Published var selectedPackageBundle: PackageBundelModel?
    @Published var packageBundles: [PackageBundelModel] = []
    
    @Published var selectedBoostPackage: PackageBoostModel?
    @Published var boostPackages: BoostPackagesModel?
    
    private weak var navigationController: UINavigationController?
    private var carRepository = CarRepositry()
    
    init(navigationController: UINavigationController?) {
        self.navigationController = navigationController
    }
    
    func goBack() {
        navigationController?.popViewController(animated: true)
    }
    
    func onContinue() {
        guard let navigationController = navigationController else { return }
        SellingMultipleRouter.moveToBoostVisibility(from: navigationController, viewModel: self)
    }
    
    func getContact() {
    }
    
//    func viewSingleDeals() {
//    }
    
    func getHelp() {
    }
    
    func skipBoost() {
        guard let navigationController = navigationController else { return }
        self.selectedBoostPackage = nil
        SellingMultipleRouter.moveToBuyBundleForMicroDealerCheckout(from: navigationController, viewModel: self)
    }
    
    func continueBoost() {
        guard let navigationController = navigationController else { return }
        SellingMultipleRouter.moveToBuyBundleForMicroDealerCheckout(from: navigationController, viewModel: self)
    }
    
    func getPackageBundlesDetails(isBundle: Bool, includeSelfService: Bool) {
        self.carRepository.getPackageBundlesDetails(isBundle: isBundle, includeSelfService: includeSelfService) {  [weak self] result in
            DispatchQueue.main.async {
                self?.packageBundles = result ?? []
                
                if self?.selectedPackageBundle == nil {
                    self?.selectedPackageBundle = self?.packageBundles.first(where: { $0.packageId == 23 })
                }
            }
        }
    }
    
    func getBoostPackages() {
        self.carRepository.getBoostPackages() { [weak self] result in
            DispatchQueue.main.async {
                self?.boostPackages = result
                
                if self?.selectedBoostPackage == nil {
                    self?.selectedBoostPackage = self?.boostPackages?.Packages?.first(where: { $0.Id == 1 })
                }
            }
        }
    }
    
    public func buyBundleMicroDealerFlow(boostPackageId: Int, paymentType: Int, packageId: Int, discountCodeId: Int? = nil, useWallet: Bool, adId: Int, completion: @escaping (Result?) -> Void) {
        self.carRepository.buyBundleMicroDealerFlow(useWallet: useWallet, packageId: packageId, boostPackageId: boostPackageId, discountCodeId: discountCodeId, paymentType: paymentType) { result in
            completion(result)
        }
    }
    
    public func validateDiscountCode(code: String, estimatedPrice: Int, packageId: Int, completion: @escaping (Result?) -> Void) {
        self.carRepository.sycPromoCodeValidateForMicroDealerBuyBundleFlow(code: code, estimatedPrice: estimatedPrice, packageId: packageId) { result in
            completion(result)
        }
    }
}
