//
//  ApplePayConfirmationPaymentVC.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 02/09/2024.
//  Copyright © 2024 Bola <PERSON>z. All rights reserved.
//

import UIKit

enum ApplePayConfirmationPaymentVCStatus: Int {
    case success = 0
    case failure = 1
}

enum ApplePayConfirmationPaymentVCScreenType: Int {
    case sellingSteps = 0
    case marketplace = 1
}

class ApplePayConfirmationPaymentVC: BaseVC {
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var closeIcon: UIImageView!
    @IBOutlet weak var stickyButtonView: UIView!
    @IBOutlet weak var goToDashboardButton: buttonLocalization!
    
    private var screenStatus: ApplePayConfirmationPaymentVCStatus = .failure
    private var dic: [String: Any] = [:]
    private var adId: Int = 0
    private var selectedPackage: LstPackages?
    private var userData: [String: Any]?
    private var isTrim = false
    private var promoCodeDiscountAmount = 0.0
    var goToDashboardCallback: ((Int) -> Void)?
    private var screenType: ApplePayConfirmationPaymentVCScreenType?
    private var isOpenFromBoost = false
    private var isOpenFromMicroDealer = false
    private var boostService: BoostServices?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        self.navigationItem.backButtonTitle = " "
        
        tableView.dataSource = self
        tableView.delegate = self
        tableView.showsVerticalScrollIndicator = false
        tableView.separatorStyle = .none
        tableView.register(ApplePayConfirmationTVC.nib(), forCellReuseIdentifier: ApplePayConfirmationTVC.identifier)
        
        if self.screenStatus == .success {
            self.closeIcon.isHidden = true
            self.stickyButtonView.isHidden = false
            self.stickyButtonView.addTopShadow(
                shadowColor: Colors.topShadowBorderColor,
                shadowOpacity: 1,
                shadowRadius: 20,
                offset: CGSize(width: 0, height: 4)
            )
            if self.screenType == .sellingSteps {
                self.goToDashboardButton.setTitle("Go to dashboard".localized, for: .normal)
            } else {
                self.goToDashboardButton.setTitle(isOpenFromBoost || isOpenFromMicroDealer ? "Go to dashboard".localized : "Go to my orders".localized, for: .normal)
            }
            self.goToDashboardButton.setTitleColor(Colors.deepSkyBlueColor, for: .normal)
            self.goToDashboardButton.cornerRadius = 8

            var config = UIButton.Configuration.plain()
            config.titleTextAttributesTransformer = UIConfigurationTextAttributesTransformer({ incoming in
                var outgoing = incoming
                outgoing.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: 14)
                return outgoing
            })
            
            self.goToDashboardButton.configuration = config
            self.goToDashboardButton.backgroundColor = Colors.deepSkyBlueColor.withAlphaComponent(0.1)
            self.goToDashboardButton.isUserInteractionEnabled = true
            self.goToDashboardButton.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(goToDashboardAction)))
        } else {
            self.closeIcon.isHidden = false
            self.closeIcon.isUserInteractionEnabled = true
            self.closeIcon.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(dismissScreen)))
            self.stickyButtonView.isHidden = true
        }
    }
    
    @objc
    private func goToDashboardAction() {
        self.dismiss(animated: false)
        
        self.goToDashboardCallback?(self.adId)
    }
    
    @objc
    private func dismissScreen() {
        self.dismiss(animated: true)
    }
    
    public func setScreenStatus(screenStatus: ApplePayConfirmationPaymentVCStatus, dic: [String: Any], adId: Int, selectedPackage: LstPackages?, userData: [String: Any]?, isTrim: Bool, promoCodeDiscountAmount: Double, screenType: ApplePayConfirmationPaymentVCScreenType, isOpenFromBoost: Bool = false, boostService: BoostServices? = nil, isOpenFromMicroDealer: Bool = false) {
        self.screenStatus = screenStatus
        self.dic = dic
        self.adId = adId
        self.selectedPackage = selectedPackage
        self.userData = userData
        self.isTrim = isTrim
        self.promoCodeDiscountAmount = promoCodeDiscountAmount
        self.screenType = screenType
        self.isOpenFromBoost = isOpenFromBoost
        self.boostService = boostService
        self.isOpenFromMicroDealer = isOpenFromMicroDealer
    }
}

extension ApplePayConfirmationPaymentVC: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return 1
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: ApplePayConfirmationTVC.identifier, for: indexPath) as? ApplePayConfirmationTVC
        cell?.selectionStyle = .none
        if self.screenType == .sellingSteps {
            cell?.configureCell(screenStatus: self.screenStatus, dic: self.dic, selectedPackage: self.selectedPackage, userData: self.userData, isTrim: self.isTrim, promoCodeDiscountAmount: self.promoCodeDiscountAmount)
        } else {
            cell?.configureCellForMarketPlace(screenStatus: self.screenStatus, dic: self.dic, userData: self.userData, isTrim: self.isTrim, promoCodeDiscountAmount: self.promoCodeDiscountAmount, isOpenFromBoost: self.isOpenFromBoost, boostService: self.boostService)
        }
        return cell ?? UITableViewCell()
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }
}
