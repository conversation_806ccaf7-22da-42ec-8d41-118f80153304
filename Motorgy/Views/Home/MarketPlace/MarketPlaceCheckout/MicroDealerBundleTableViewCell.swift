//
//  MicroDealerBundleTableViewCell.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 14/07/2025.
//  Copyright © 2025 <PERSON><PERSON>. All rights reserved.
//


import SwiftUI

class MicroDealerBundleTableViewCell: UITableViewCell, CellReusableIdentifier {
    private var hostingController: UIHostingController<CarSummaryView>?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupCell()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupCell()
    }
    
    private func setupCell() {
        backgroundColor = .clear
        selectionStyle = .none
        separatorInset = UIEdgeInsets(top: 0, left: 24, bottom: 0, right: 24)
    }
    
    func configure(with selectedPackageBundle: PackageBundelModel, editBoostAction: (() -> Void)?) {
        hostingController?.view.removeFromSuperview()
        hostingController?.removeFromParent()
        
        let swiftUIView = CarSummaryView(selectedPackageBundle: selectedPackageBundle, editBoostAction: editBoostAction)
        let hostingController = UIHostingController(rootView: swiftUIView)
        hostingController.view.backgroundColor = .clear
        self.hostingController = hostingController
        
        contentView.addSubview(hostingController.view)
        hostingController.view.translatesAutoresizingMaskIntoConstraints = false
        hostingController.view.semanticContentAttribute = !LanguageHelper.isEnglish ? .forceRightToLeft : .forceLeftToRight
        
        NSLayoutConstraint.activate([
            hostingController.view.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            hostingController.view.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            hostingController.view.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            hostingController.view.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -12)
        ])
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        hostingController?.view.removeFromSuperview()
        hostingController?.removeFromParent()
        hostingController = nil
    }
}

struct CarSummaryView: View {
    let selectedPackageBundle: PackageBundelModel
    var editBoostAction: (() -> Void)?
    
    var body: some View {
        HStack(spacing: 16) {
            Text(selectedPackageBundle.packageName ?? "")
                .font(Font.custom(LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 14 : 12))
                .foregroundColor(.black)
            
            Spacer()
            
            HStack(spacing: 16) {
                Text("\(selectedPackageBundle.price?.formattedWithWithoutFraction() ?? "") \("KWD".localized)")
                    .font(
                        Font.custom(LanguageHelper.isEnglish ? "Inter" : "Cairo", size: 14)
                            .weight(.bold)
                    )
                    .foregroundColor(.primary)
                
                Button(action: {
                    editBoostAction?()
                }) {
                    Image("edit_sell_car")
                        .resizable()
                        .frame(width: 32, height: 32)
                }
            }
        }
        .padding(16)
        .background(Color(.white))
        .cornerRadius(12)
        .environment(\.layoutDirection, LanguageHelper.isEnglish ? .leftToRight : .rightToLeft)
    }
}
