//
//  ExtraServiceCVC.swift
//  Motorgy
//
//  Created by <PERSON><PERSON><PERSON><PERSON> Ashraf on 06/11/2024.
//  Copyright © 2024 <PERSON><PERSON>. All rights reserved.
//

import UIKit

protocol ExtraServiceCVCDelegate: AnyObject {
    func didInfoTapped(extraService: ExtraServices?)
    func didSelectExtraService(extraService: ExtraServices?)
}

class ExtraServiceCVC: UICollectionViewCell, CellReusableIdentifier {
    @IBOutlet weak var contianerView: UIView!
    @IBOutlet weak var titleLabel: labelLocalization!
    @IBOutlet weak var priceLabel: labelLocalization!
    @IBOutlet weak var discountLabel: labelLocalization!
    @IBOutlet weak var addView: UIView!
    @IBOutlet weak var addLabel: labelLocalization!
    @IBOutlet weak var infoIcon: UIImageView!
    
    private var extraService: ExtraServices?
    weak var delegate: ExtraServiceCVCDelegate?

    func configureCell(extraService: ExtraServices?, controller: PackageDetailsExtraServicesTVC?) {
        backgroundColor = .clear
        
        self.extraService = extraService
        self.delegate = controller
        
        infoIcon.isUserInteractionEnabled = true
        infoIcon.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(infoIconTapped)))
//        infoIcon.isHidden = extraService?.description?.isEmpty ?? false || extraService?.descriptionEn?.isEmpty ?? false || extraService?.descriptionAr?.isEmpty ?? false
        infoIcon.isHidden = true
        
        titleLabel.textColor = Colors.charcoalColor
        titleLabel.text = LanguageHelper.isEnglish ? extraService?.name ?? "" : extraService?.name ?? ""
        titleLabel.numberOfLines = 2
        titleLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 14 : 12)
        
        discountLabel.textColor = Colors.slateColor
        discountLabel.isHidden = extraService?.discountedPrice == 0.0
        discountLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-Regular" : "Cairo-Regular", size: LanguageHelper.isEnglish ? 12 : 10)
        
        let attributedString = NSMutableAttributedString(string: "\((extraService?.price ?? 0.0).formattedWithWithoutFraction()) " + "KWD".localized)
        attributedString.addAttribute(
            .strikethroughStyle,
            value: NSUnderlineStyle.single.rawValue,
            range: NSRange(location: 0, length: attributedString.length)
        )
        
        discountLabel.attributedText = attributedString
        
        // Safe price calculation to prevent division by zero
        let discountedPrice = extraService?.discountedPrice ?? 0.0
        let originalPrice = extraService?.price ?? 0.0
        let finalPrice = (discountedPrice > 0) ? discountedPrice : originalPrice

        // Ensure we have a valid price before formatting
        if finalPrice > 0 {
            priceLabel.text = "\(finalPrice.formattedWithWithoutFraction()) " + "KWD".localized
        } else {
            priceLabel.text = "0 " + "KWD".localized
        }
        priceLabel.textColor = extraService?.discountedPrice == 0.0 ? Colors.charcoalColor : Colors.grapefruitColor
        priceLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 12 : 10)
        
        contianerView.layer.borderColor = UIColor.hexStringToUIColor(hex: "#EAECF0").cgColor
        contianerView.layer.borderWidth = 1
        contianerView.cornerRadius = 8
        contianerView.backgroundColor = .white
        
        addView.cornerRadius = 8
        addView.isUserInteractionEnabled = true
        addView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(addViewTapped)))
        
        addLabel.textAlignment = .center
        addLabel.font = UIFont(name: LanguageHelper.isEnglish ? "Inter-SemiBold" : "Cairo-SemiBold", size: LanguageHelper.isEnglish ? 14 : 14)
        
        if self.extraService?.isSelected ?? false {
            addView.backgroundColor = UIColor.hexStringToUIColor(hex: "#FF453B").withAlphaComponent(0.2)
            addView.layer.borderColor = UIColor.clear.cgColor
            addView.layer.borderWidth = 0
            
            addLabel.textColor = UIColor.hexStringToUIColor(hex: "#FF453B")
            addLabel.text = "Remove".localized
        } else {
            addView.backgroundColor = .white
            addView.layer.borderColor = Colors.bluishColor.cgColor
            addView.layer.borderWidth = 1
            
            addLabel.textColor = Colors.bluishColor
            addLabel.text = "Add".localized
        }
    }

    @objc 
    func infoIconTapped() {
        self.delegate?.didInfoTapped(extraService: self.extraService)
    }
    
    @objc
    func addViewTapped() {
        self.delegate?.didSelectExtraService(extraService: self.extraService)
    }
}
