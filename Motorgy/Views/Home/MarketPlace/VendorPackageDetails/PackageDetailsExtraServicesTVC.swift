//
//  PackageDetailsExtraServicesTVC.swift
//  Motorgy
//
//  Created by Abanoub Ashraf on 06/11/2024.
//  Copyright © 2024 <PERSON><PERSON>z. All rights reserved.
//

import UIKit

class PackageDetailsExtraServicesTVC: UITableViewCell, CellReusableIdentifier {
    @IBOutlet weak var collectionView: UICollectionView!
    @IBOutlet weak var collectionViewHeight: NSLayoutConstraint!
    
    private var package: Packages?
    weak var delegate: ExtraServiceCVCDelegate?
    
    func configureCell(package: Packages?, controller: VendorPackageDetailsScreenVC?) {
        self.package = package
        self.delegate = controller
        
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.backgroundColor = .clear
        collectionView.register(ExtraServiceCVC.nib(), forCellWithReuseIdentifier: ExtraServiceCVC.identifier)
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        collectionView.collectionViewLayout = layout
        collectionView.showsHorizontalScrollIndicator = false
        self.collectionView.reloadData()
        
        DispatchQueue.main.async { [weak self] in
            self?.collectionViewHeight.constant = LanguageHelper.isEnglish ? 110 : 112
        }
    }
}

extension PackageDetailsExtraServicesTVC: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return self.package?.extraServices?.count ?? 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: ExtraServiceCVC.identifier, for: indexPath) as! ExtraServiceCVC
        cell.configureCell(extraService: self.package?.extraServices?[indexPath.row], controller: self)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 140, height: LanguageHelper.isEnglish ? 110 : 112)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return 12
    }
}

extension PackageDetailsExtraServicesTVC: ExtraServiceCVCDelegate {
    func didInfoTapped(extraService: ExtraServices?) {
        self.delegate?.didInfoTapped(extraService: extraService)
    }
    
    func didSelectExtraService(extraService: ExtraServices?) {
        self.delegate?.didSelectExtraService(extraService: extraService)
    }
}
