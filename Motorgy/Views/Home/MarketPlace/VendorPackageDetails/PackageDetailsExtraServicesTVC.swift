//
//  PackageDetailsExtraServicesTVC.swift
//  Motorgy
//
//  Created by <PERSON>ban<PERSON>b Ashraf on 06/11/2024.
//  Copyright © 2024 <PERSON><PERSON>z. All rights reserved.
//

import UIKit

class PackageDetailsExtraServicesTVC: UITableViewCell, CellReusableIdentifier {
    @IBOutlet weak var collectionView: UICollectionView!
    @IBOutlet weak var collectionViewHeight: NSLayoutConstraint!

    private var package: Packages?
    weak var delegate: ExtraServiceCVCDelegate?
    private var isLayoutSetup = false
    
    func configureCell(package: Packages?, controller: VendorPackageDetailsScreenVC?) {
        self.package = package
        self.delegate = controller

        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.backgroundColor = .clear
        collectionView.register(ExtraServiceCVC.nib(), forCellWithReuseIdentifier: ExtraServiceCVC.identifier)

        // Setup layout with safety checks
        setupCollectionViewLayout()

        collectionView.showsHorizontalScrollIndicator = false
        self.collectionView.reloadData()

        DispatchQueue.main.async { [weak self] in
            self?.collectionViewHeight.constant = LanguageHelper.isEnglish ? 110 : 112
        }
    }

    private func setupCollectionViewLayout() {
        // Ensure collection view has valid bounds before setting layout
        guard collectionView.bounds.width > 0 && collectionView.bounds.height > 0 else {
            // Defer layout setup until the view is properly laid out
            DispatchQueue.main.async { [weak self] in
                self?.setupCollectionViewLayout()
            }
            return
        }

        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumInteritemSpacing = 12
        layout.minimumLineSpacing = 12
        layout.sectionInset = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16)

        // Only set layout if collection view is ready
        if collectionView.collectionViewLayout != layout {
            collectionView.collectionViewLayout = layout
        }

        isLayoutSetup = true
    }

    override func layoutSubviews() {
        super.layoutSubviews()

        // Ensure layout is properly set up after the view has been laid out
        if !isLayoutSetup && collectionView.bounds.width > 0 {
            setupCollectionViewLayout()
        }
    }
}

extension PackageDetailsExtraServicesTVC: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return self.package?.extraServices?.count ?? 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: ExtraServiceCVC.identifier, for: indexPath) as! ExtraServiceCVC
        cell.configureCell(extraService: self.package?.extraServices?[indexPath.row], controller: self)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        // Safety check to prevent division by zero
        guard collectionView.bounds.width > 0 && collectionView.bounds.height > 0 else {
            return CGSize(width: 140, height: LanguageHelper.isEnglish ? 110 : 112)
        }

        // Calculate safe width based on collection view bounds
        let availableWidth = collectionView.bounds.width - 32 // Account for section insets
        let itemWidth = min(140, max(120, availableWidth / 3)) // Ensure reasonable width
        let itemHeight = LanguageHelper.isEnglish ? 110 : 112

        return CGSize(width: itemWidth, height: itemHeight)
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return 12
    }
}

extension PackageDetailsExtraServicesTVC: ExtraServiceCVCDelegate {
    func didInfoTapped(extraService: ExtraServices?) {
        self.delegate?.didInfoTapped(extraService: extraService)
    }
    
    func didSelectExtraService(extraService: ExtraServices?) {
        self.delegate?.didSelectExtraService(extraService: extraService)
    }
}
