//
//  CarRepositry.swift
//  Motorgy
//
//  Created by ahmed ezz on 4/5/20.
//  Copyright © 2020 ahmed ezz. All rights reserved.
//

import UIKit
import RxSwift
import SwiftyJSON
import DeviceKit

class CarRepositry: Repositry {
    
    // MARK: - postCallBackRequest
    public func postCallBackRequest(year: Int, mileage: Int, brandID: Int, modelID: Int, trimID: Int, estimatedPrice: Int, packageId: Int) -> Observable<Result?> {
        let deviceToken = SharedHelper.shared.getFromDefault(key: "deviceToken")
        let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? ""

        let data: [String: Any] = [
            "Platform": 1,
            "Devicename": Device.current.description,
            "Year": year,
            "Mileage": mileage,
            "DeviceToken": deviceToken,
            "DeviceID": deviceId,
            "BrandID": brandID,
            "ModelID": modelID,
            "TrimID": trimID,
            "EstimatedPrice": estimatedPrice,
            "PackageId": packageId
        ]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/PostCallBackRequest",data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - getPackageDetails
    public func getPackageDetails(modelId: Int, year: Int, mileage: Int, isSelfSellingCar: Bool, adId: Int?, isFromUpgrade: Bool?, estimatedPrice: Double?) -> Observable<[LstPackages]?> {
        var data: [String: Any] = [
            "Platform": 1,
            "Devicename": Device.current.description,
            "ModelId": modelId,
            "Year": year,
            "Mileage": mileage,
            "IncludeSelfService": isSelfSellingCar
        ]
        
        if let adId = adId {
            data["AdID"] = adId
        }
        
        if let isFromUpgrade = isFromUpgrade {
            data["IsFromUpgrade"] = isFromUpgrade
        }
        
        if let estimatedPrice = estimatedPrice {
            data["EstimatedPrice"] = estimatedPrice
        }
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/GetPackageDetails",data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode([LstPackages].self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - getSellYourCarDetails
    public func getSellYourCarDetails(pageId:Int = 1) -> Observable<LandingSellCarModel?> {
        
        let data:[String:Any] = ["Platform":1, "Devicename": Device.current.description]
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Home/GetSellYourCarDetails",data: data, pageId: pageId, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(LandingSellCarModel.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - getUserActiviries
    public func getUserActiviriesDetails(adID: Int) -> Observable<CustomerActivity?> {
        
        let data:[String:Any] = ["Platform":1, "Devicename": Device.current.description, "adID": adID]
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Customer/GetCustomerActivityDetails", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(CustomerActivity.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - getUserActiviries
    public func getUserActiviries(pageId:Int = 1) -> Observable<BuyerModel?> {
        
        let data:[String:Any] = ["PageSize": 10, "Platform":1, "Devicename": Device.current.description]
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Customer/GetBuyerActivity",data: data, pageId: pageId, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(BuyerModel.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - getBuyerCarDetails
    public func getBuyerCarDetails(carID: Int) -> Observable<BuyerCarDetailsModel?> {
        
        let data:[String:Any] = ["AdId": carID, "Platform":1, "Devicename": Device.current.description]
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Customer/GetBuyerActivityDetails",data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(BuyerCarDetailsModel.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - getBuyerCarDetailsHistory
    public func getBuyerCarDetailsHistory(carID: Int) -> Observable<BuyerHistory?> {
        
        let data:[String:Any] = ["AdId": carID, "Platform":1, "Devicename": Device.current.description]
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Customer/GetBuyerActivityHistoryDetails",data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(BuyerHistory.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - getCarDetails
    public func getCarDetails() -> Observable<BrandModelResult?> {
        
        let data:[String:Any] = ["UserID": 1, "UserTypeID": 1, "Platform":1, "Devicename": Device.current.description]
        return  Observable.create { observer in
            if  SharedHelper.shared.getFromDefault(key: "detailsResponse").isEmpty {
                self.getConnection().sendData(urlName: "InstantCashOffer/GetAllCarDetails",data: data,withoutUser: true, headers: self.getHeader(), showLoading: false).bind { response in
                    if response as? String != "fail" {
                        let jsonResponse = JSON(response ?? [:])
                        SharedHelper.shared.saveInDefault(key: "detailsResponse", value: jsonResponse.rawString() ?? "" )
                        if let resultModel = CodableHandler.decode(BrandModelResult.self, from: jsonResponse) {
                            observer.onNext(resultModel)
                        } else {
                            observer.onNext(nil)
                        }
                    } else {
                        let response = SharedHelper.shared.getFromDefault(key: "detailsResponse")
                        let jsonResponse = JSON(response)
                        if let resultModel = CodableHandler.decode(BrandModelResult.self, from: jsonResponse) {
                            observer.onNext(resultModel)
                        } else {
                            observer.onNext(nil)
                        }
                    }
                }
            } else {
                let response = SharedHelper.shared.getFromDefault(key: "detailsResponse")
                let jsonResponse = JSON(response)
                if let resultModel = CodableHandler.decode(BrandModelResult.self, from: jsonResponse) {
                    observer.onNext(resultModel)
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - EditInstantCashOffer
    public func editUploadedAd(data:[String:Any], images:[File]? = nil, file:File? = nil, price: Int, id: Int) -> Observable<FeaturedAd?> {
        var headerData:[String:Any] = ["ID":id, "EstimatedPrice":price]
        
        headerData = headerData.merging(data) { $1 }
        
        return  Observable.create { observer in
            self.getConnection().uploadAd(urlName: "InstantCashOffer/EditInstantCashOffer",data: headerData , file: file, images: images, headers: self.getHeader())
                .bind { response in
                    let json = JSON(response ?? [:])
                    
                    if response as? String != "fail" {
                        if let featuredAd = CodableHandler.decodeClass(FeaturedAd.self, classJsonData: json) {
                            observer.onNext(featuredAd)
                        }
                    } else {
                        if let featuredAd = CodableHandler.decodeClass(FeaturedAd.self, classJsonData: json) {
                            observer.onNext(featuredAd)
                        }
                    }
                }
            return Disposables.create()
        }
    }
    
    // MARK: - uploadAd
    public func uploadAd(data:[String:Any], images:[File]? = nil, file:File? = nil, price: Int, name: String, email: String, mobile: String) -> Observable<FeaturedAd?> {
        
        let receiveNotifications = AppHelper.shared.pushNotificationStatus

        var headerData:[String:Any] = ["FullName": price, "Email": price, "MobileNumber": price, "EstimatedPrice": price, "ReceiveNotifications" : receiveNotifications]
        
        headerData = headerData.merging(data) { $1 }
        
        return  Observable.create { observer in
            self.getConnection().uploadAd(urlName: "InstantCashOffer/PostInstantCashOffer",data: headerData , file: file, images: images, headers: self.getHeader())
                .bind { response in
                    let json = JSON(response ?? [:])
                    
                    if response as? String != "fail" {
                        if let featuredAd = CodableHandler.decodeClass(FeaturedAd.self, classJsonData: json) {
                            observer.onNext(featuredAd)
                        }
                    } else {
                        if let featuredAd = CodableHandler.decodeClass(FeaturedAd.self, classJsonData: json) {
                            observer.onNext(featuredAd)
                        }
                    }
                }
            return Disposables.create()
        }
    }
    
    // MARK: - uploadService
    public func uploadService(data: [Int], brandID: Int, modelID: Int, year: Int, email: String, mobileNumber: String, totalFees: Int) -> Observable<ServiceSuccessModel?> {
        
        let headerData:[String:Any] = ["Devicename": Device.current.description, "Platform": 1, "LstServiceID":data, "BrandID":brandID, "ModelID":modelID, "Year":year, "Email":email, "MobileNumber":mobileNumber, "TotalFees":totalFees]
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Services/BookService",data: headerData, headers: self.getHeader()).bind { response in
                let json = JSON(response ?? [:])
                
                if response as? String != "fail" {
                    if let featuredAd = CodableHandler.decode(ServiceSuccessModel.self, from: json) {
                        observer.onNext(featuredAd)
                    }
                } else {
                    if let featuredAd = CodableHandler.decode(ServiceSuccessModel.self, from: json) {
                        observer.onNext(featuredAd)
                    }
                }
            }
            return Disposables.create()
        }
    }

    public func findResultsForConceirge(brandID: Int, modelID: Int, year: Int, email: String, mobileNumber: String, additionalDetails: String, estimatedPrice: Double) -> Observable<ConceirgeModel?> {
        let headerData: [String: Any] = [
            "Devicename": Device.current.description,
            "Platform": 1,
            "LstServiceID": [5],
            "BrandID": brandID,
            "ModelID": modelID,
            "TrimID": 0,
            "Year": year,
            "Email": email,
            "MobileNumber": mobileNumber,
            "AdditionalDetails": additionalDetails,
            "EstimatedPrice": estimatedPrice
        ]
        
        return Observable.create { observer in
            self.getConnection().sendData(urlName: "Services/CarBuyingService", data: headerData, headers: self.getHeader()).bind { response in
                let json = JSON(response ?? [:])
                
                if response as? String != "fail" {
                    if let conceirgeModel = CodableHandler.decode(ConceirgeModel.self, from: json) as? ConceirgeModel {
                        observer.onNext(conceirgeModel)
                    }
                } else {
                    if let conceirgeModel = CodableHandler.decode(ConceirgeModel.self, from: json) as? ConceirgeModel {
                        observer.onNext(conceirgeModel)
                    }
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - getMyCars
    public func getMyCars(pageId:Int = 1) -> Observable<Result?> {
        
        let data:[String:Any] = ["PageSize":10, "Platform":1, "Devicename": Device.current.description]
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/GetMyCars",data: data,pageId: pageId, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - getMyCarDetails
    public func getMyCarDetails(carId: Int, completion: @escaping (CarStatusModel?) -> Void) {
        let data:[String:Any] = ["CarID":carId, "Platform":1, "Devicename": Device.current.description]
        
        self.getConnection().sendDataURLSession(urlName: "InstantCashOffer/GetCarDetails_Dashboard",data: data, headers: self.getHeader()) { response in
            guard response as? String != "fail" else {
                completion(nil)
                return
            }
            
            let jsonResponse = JSON(response ?? [:])
            let result = CodableHandler.decode(CarStatusModel.self, from: jsonResponse)
            completion(result)
        }
    }
    
    public func getMyCarDetailsSelfService(carId: Int, completion: @escaping (CarStatusModel?) -> Void) {
        let data: [String: Any] = ["CarID": carId, "Platform": 1, "Devicename": Device.current.description]
        
        self.getConnection().sendDataURLSession(urlName: "InstantCashOffer/GetSelfServiceCarDetails", data: data, headers: self.getHeader()) { response in
            guard response as? String != "fail" else {
                completion(nil)
                return
            }
            let jsonResponse = JSON(response ?? [:])
            let result = CodableHandler.decode(CarStatusModel.self, from: jsonResponse)
            completion(result)
        }
    }
    
    // MARK: - acceptOffer
    public func acceptOffer(carId:Int) -> Observable<CarStatusModel?> {
        
        let data:[String:Any] = ["instantCashID":carId, "Platform":1, "Devicename": Device.current.description]
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/AcceptPrice",data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(CarStatusModel.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - refuseOffer
    public func refuseOffer(carId: Int, reason: String) -> Observable<CarStatusModel?> {
        
        let data:[String:Any] = ["instantCashID":carId, "Reason":reason, "Platform":1, "Devicename": Device.current.description]
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/RejectPrice",data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(CarStatusModel.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - carViewingComfirm
    public func carViewingComfirm(id: Int) -> Observable<CarStatusModel?> {
        
        let data:[String:Any] = ["Id":id, "Type":5, "Platform":1, "Devicename": Device.current.description]
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/CarViewingComfirm",data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(CarStatusModel.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - carViewingCanceled
    public func carViewingCanceled(id: Int) -> Observable<CarStatusModel?> {
        
        let data:[String:Any] = ["Id":id, "Type":5, "Platform":1, "Devicename": Device.current.description]
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/CarViewingCanceled",data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(CarStatusModel.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - carViewingCanceled
    public func notifyMe(id: Int) -> Observable<Result?> {
        
        let data:[String:Any] = ["AdID":id, "Platform":1, "Devicename": Device.current.description]
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Customer/GetBuyerNotifyMe",data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - updatePrice
    public func updatePrice(id: Int, price: Int) -> Observable<Result?> {
        
        let data:[String:Any] = ["Price": price, "AdID": id, "Type": 0, "Platform": 1, "Devicename": Device.current.description]
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/ReducePrice",data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - postAbandonedRequest
    public func postAbandonedRequest(brandID: Int, modelID: Int, trimID: Int, year: Int, mileage: Int, paintID: Int, optionID: Int, specificationID: Int, estimatedPrice: Int, requestFrom: Int? = nil, inspectionLocation: Int? = nil, inspectionDate: String? = nil, timeslotIds: String? = nil, packageId: Int? = nil) -> Observable<Result?> {
        
        let deviceToken = SharedHelper.shared.getFromDefault(key: "deviceToken")
        
        let receiveNotifications = AppHelper.shared.pushNotificationStatus
        
        var data: [String:Any] = [
            "DevicePlatform" : 1,
            "Devicename" : Device.current.description,
            "DeviceToken" : deviceToken,
            "ReceiveNotifications" : receiveNotifications,
            "BrandID" : brandID,
            "ModelID" : modelID,
            "TrimID": trimID,
            "Year": year,
            "Mileage": Double(mileage),
            "PaintID": paintID,
            "OptionID": optionID,
            "SpecificationID": specificationID,
            "EstimatedPrice": Double(estimatedPrice),
            "FullName": SharedHelper.shared.getFromDefault(key: "fullName"),
            "Email": SharedHelper.shared.getFromDefault(key: "email"),
            "MobileNumber": SharedHelper.shared.getFromDefault(key: "mobile"),
            "AbandonedRequestID": ConstantsValues.sharedInstance.abandonedRequestID
        ]
        
        if requestFrom != nil {
            let dataExtra = ["RequestFrom": requestFrom ?? 0]
            data = data.merging(dataExtra) { $1 }
        }
        
        if inspectionLocation != nil {
            let dataExtra = ["InspectionLocation": inspectionLocation ?? 0]
            data = data.merging(dataExtra) { $1 }
        }
        
        if inspectionDate != nil {
            let dataExtra = ["InspectionDate": inspectionDate ?? ""]
            data = data.merging(dataExtra) { $1 }
        }
        
        if timeslotIds != nil {
            let dataExtra = ["TimeslotIds": timeslotIds ?? ""]
            data = data.merging(dataExtra) { $1 }
        }
        
        if packageId != nil {
            let dataExtra = ["PackageId": packageId ?? 0]
            data = data.merging(dataExtra) { $1 }
        }
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/PostAbandonedRequest", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - getCancelFeedbackReasons
    public func getCancelFeedbackReasons() -> Observable<Result?> {
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/GetCancelFeedbackReasons", headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - cancelSYR
    public func cancelSYR(description: String, lstServiceID: [Int], AdID: Int) -> Observable<Result?> {
        let data: [String: Any] = ["AdId": AdID, "UserID": 0, "FeedbackMessage": description, "FeedbackIds": lstServiceID, "Platform": 1, "Devicename": Device.current.description]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/CancelSYR", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - getAvailableTimeSlotsForDateRange
    public func getAvailableTimeSlotsForDateRange(startDate: String, inspectionLocationId: Int) -> Observable<[TimeSlotsForDateRange]?> {
        let data: [String: Any] = ["StartDate": startDate, "UserID": 0, "InspectionLocationId": inspectionLocationId, "Platform": 1]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Booking/GetAvailableTimeSlotsForDateRange", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode([TimeSlotsForDateRange].self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - checkTimeSlotAvailability
    public func checkTimeSlotAvailability(inspectionDate: String, slotIdsString: String, lat: Double, lng: Double) -> Observable<TimeSlotAvailability?> {
        let data: [String: Any] = ["InspectionDate": inspectionDate, "UserID": 0, "SlotIdsString": slotIdsString, "Platform": 1, "Lat": lat, "Lng": lng]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Booking/CheckTimeSlotAvailability", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(TimeSlotAvailability.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - postSYC
    public func postSYC(userDataStrings: [String: Any], userDataInts: [String: Int], adId: Int, lat: Double, long: Double, discountCodeId: Int? = nil, walletBalance: Double, useWallet: Bool) -> Observable<Result?> {
        let deviceToken = SharedHelper.shared.getFromDefault(key: "deviceToken")
        let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? ""

        var data: [String: Any] = [
            "UserID": 0,
            "Platform": 1,
            "Devicename": Device.current.description,
            "DeviceToken": deviceToken,
            "DeviceID": deviceId,
            "AdID": adId,
            "Lat": lat,
            "Long": long,
            "WalletBalance": walletBalance,
            "UseWallet": useWallet
        ]
        
        data = data.merging(userDataStrings) { $1 }
        
        data = data.merging(userDataInts) { $1 }
        
        if let discountCodeId = discountCodeId {
            data["DiscountCodeId"] = discountCodeId
        }
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/PostSYC", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - releaseTimeslot
    public func releaseTimeslot(adId: Int) -> Observable<TimeSlotAvailability?> {
        let data: [String: Any] = ["UserID": 0, "Platform": 1, "AdID": adId]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Booking/ReleaseTimeslot", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(TimeSlotAvailability.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    public func marketPlaceCancelRequest(serviceRequestId: Int) -> Observable<Result?> {
        let data: [String: Any] = ["ServiceRequestId": serviceRequestId]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "MarketPlace/CancelRequest", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - getSelfServiceDetails
    public func getSelfServiceDetails() -> Observable<Result?> {
        return Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/GetSelfServiceDetails", headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    // MARK: - uploadSelfService
    public func uploadSelfService(stringToIntData: [String: Any]?, stringToIdsData: [String: Any]?, images:[UploadImageItemModel]? = nil, file:File? = nil, carDetailsData: [String: Any]?, adId: Int? = nil, bodyDamageParts: [CustomBodyPart], isFromEdit: Bool, additionalInfo: String?, discountCodeId: Int? = nil, switchUsingWallet: Bool, walletAvailableBalance: Double, isWantMotorgyOffer: Bool) -> Observable<PostApiResponse?> {
        var headerData: [String: Any] = [:]
        
        headerData = headerData.merging(stringToIntData ?? [:]) { $1 }

        headerData = headerData.merging(carDetailsData ?? [:]) { $1 }
        
        var convertedDict: [String: Any] = [:]
        
        for (key, value) in stringToIdsData ?? [:] {
            if let intValueArray = value as? [Int] {
                for (index, element) in intValueArray.enumerated() {
                    let newKey = "\(key)[\(index)]"
                    convertedDict[newKey] = element
                }
            }
        }
        
        headerData = headerData.merging(convertedDict) { $1 }
        
        if let adId = adId {
            headerData["AdID"] = adId
        } else {
            headerData["AdID"] = 0
        }
        
        var bodyDamagePartsDictionary = [String: Any]()
        
        for (index, bodyPart) in bodyDamageParts.enumerated() {
            bodyDamagePartsDictionary["CarBodyDamageLocations[\(index)].DamageId"] = bodyPart.damageId
            bodyDamagePartsDictionary["CarBodyDamageLocations[\(index)].DamageLocation"] = bodyPart.damageLocation.rawValue
            bodyDamagePartsDictionary["CarBodyDamageLocations[\(index)].Note"] = bodyPart.note
        }
        
        headerData = headerData.merging(bodyDamagePartsDictionary) { $1 }
        
        headerData["IsFromEdit"] = isFromEdit
        
        headerData["AdditionalInfo"] = additionalInfo ?? ""
        headerData["UseWallet"] = switchUsingWallet
        headerData["WalletBalance"] = walletAvailableBalance
        headerData["IsWantMotorgyOffer"] = isWantMotorgyOffer
        
        if let discountCodeId = discountCodeId {
            headerData["DiscountCodeId"] = discountCodeId
        }
        
        return  Observable.create { observer in
            self.getConnection().uploadAd(urlName: "InstantCashOffer/PostSelfServiceRequest", data: headerData, file: file, headers: self.getHeader(), uploadedImages: images)
                .bind { response in
                    let json = JSON(response ?? [:])
                    
                    if response as? String != "fail" {
                        if let postApiResponse = CodableHandler.decodeClass(PostApiResponse.self, classJsonData: json) {
                            observer.onNext(postApiResponse)
                        }
                    } else {
                        if let postApiResponse = CodableHandler.decodeClass(PostApiResponse.self, classJsonData: json) {
                            observer.onNext(postApiResponse)
                        }
                    }
                }
            return Disposables.create()
        }
    }
    
    // MARK: - changeSelfServiceCarStatus
    public func changeSelfServiceCarStatus(adId: Int, status: SelfServiceStatus, soldByType: SelfServiceSoldType, additionalNote: String) -> Observable<Result?> {
        let data: [String: Any] = ["UserID": 0, "Platform": 1, "AdID": adId, "Status": status.rawValue, "SoldByType": soldByType.rawValue, "AdditionalNote": additionalNote]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/ChangeSelfServiceStatus", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - getSelfServiceStats
    public func getSelfServiceStats(adId: Int) -> Observable<CarStats?> {
        let data: [String: Any] = ["UserID": 0, "Platform": 1, "AdID": adId]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/GetSelfServiceStats", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(CarStats.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - getSelfServiceCarById
    public func getSelfServiceCarById(adId: Int) -> Observable<EditSelfServiceCarResponse?> {
        let data: [String: Any] = ["AdID": adId]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "InstantCashOffer/GetSelfServiceCarById", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(EditSelfServiceCarResponse.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - postEditRequest
    public func postEditRequest(stringToIntData: [String: Any]?, stringToIdsData: [String: Any]?, images: [UploadImageItemModel]? = nil, file: File? = nil, carDetailsData: [String: Any]?, adId: Int? = nil, bodyDamageParts: [CustomBodyPart], additionalInfo: String?) -> Observable<Result?> {
        
        var headerData: [String: Any] = [:]
        
        headerData = headerData.merging(stringToIntData ?? [:]) { $1 }
        
        headerData = headerData.merging(carDetailsData ?? [:]) { $1 }
        
        var convertedDict: [String: Any] = [:]
        
        for (key, value) in stringToIdsData ?? [:] {
            if let intValueArray = value as? [Int] {
                for (index, element) in intValueArray.enumerated() {
                    let newKey = "\(key)[\(index)]"
                    convertedDict[newKey] = element
                }
            }
        }
        
        headerData = headerData.merging(convertedDict) { $1 }
        
        if let adId = adId {
            headerData["AdID"] = adId
        }
        
        var bodyDamagePartsDictionary = [String: Any]()
        
        for (index, bodyPart) in bodyDamageParts.enumerated() {
            bodyDamagePartsDictionary["CarBodyDamageLocations[\(index)].DamageId"] = bodyPart.damageId
            bodyDamagePartsDictionary["CarBodyDamageLocations[\(index)].DamageLocation"] = bodyPart.damageLocation.rawValue
            bodyDamagePartsDictionary["CarBodyDamageLocations[\(index)].Note"] = bodyPart.note
        }
        
        headerData = headerData.merging(bodyDamagePartsDictionary) { $1 }
        
        headerData["AdditionalInfo"] = additionalInfo ?? ""
        
        return  Observable.create { observer in
            self.getConnection().uploadAd(urlName: "InstantCashOffer/PostEditRequest", data: headerData, file: file, headers: self.getHeader(), uploadedImages: images)
                .bind { response in
                    let json = JSON(response ?? [:])
                    
                    if response as? String != "fail" {
                        if let result = CodableHandler.decodeClass(Result.self, classJsonData: json) {
                            observer.onNext(result)
                        }
                    } else {
                        if let result = CodableHandler.decodeClass(Result.self, classJsonData: json) {
                            observer.onNext(result)
                        }
                    }
                }
            return Disposables.create()
        }
    }
    
    // MARK: - getBuyerLeadsList
    public func getBuyerLeadsList(adId: Int) -> Observable<SelfServiceBuyerLeadsResponse?> {
        let data: [String: Any] = ["AdID": adId]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Leads/GetBuyerLeadsList", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(SelfServiceBuyerLeadsResponse.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - getBuyerLeadsList
    public func getDashboardLeads(adId: Int?, buyerID: Int?) -> Observable<LeadsDetails?> {
        let data: [String: Any] = ["AdID": adId ?? 0, "BuyerId": buyerID ?? 0]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Leads/GetDashboardLeadsByBuyer", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(LeadsDetails.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - AccpetMakeOffer
    public func accpetLeadsMakeOffer(id: Int?) -> Observable<LeadsDetails?> {
        let data: [String: Any] = ["Id": id ?? 0]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Leads/AcceptMakeOffer", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(LeadsDetails.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - CancelMakeOffer
    public func cancelLeadsMakeOffer(id: Int?) -> Observable<LeadsDetails?> {
        let data: [String: Any] = ["Id": id ?? 0]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Leads/CancelMakeOffer", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(LeadsDetails.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - NegotiateMakeOffer
    public func negotiateLeadsMakeOffer(id: Int?, price: Int?) -> Observable<LeadsDetails?> {
        let data: [String: Any] = ["Id": id ?? 0, "NegotiatePrice": price ?? 0]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Leads/NegotiateMakeOffer", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(LeadsDetails.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }

    // MARK: - accpetLeadsCarViewing
    public func accpetLeadsCarViewing(id: Int?) -> Observable<LeadsDetails?> {
        let data: [String: Any] = ["Id": id ?? 0]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Leads/ConfirmCarViewing", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(LeadsDetails.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - CancelCarViewing
    public func cancelLeadsCarViewing(id: Int?) -> Observable<LeadsDetails?> {
        let data: [String: Any] = ["Id": id ?? 0]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Leads/CancelledCarViewing", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(LeadsDetails.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    // MARK: - scheduleLeadsCarViewing
    public func scheduleLeadsCarViewing(id: Int?, date: String?) -> Observable<LeadsDetails?> {
        let data: [String: Any] = ["Id": id ?? 0, "ScheduleDate": date ?? ""]
        
        return  Observable.create { observer in
            self.getConnection().sendData(urlName: "Leads/RescheduleCarViewing", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    
                    if let result = CodableHandler.decode(LeadsDetails.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            
            return Disposables.create()
        }
    }
    
    public func repostCar(adId: Int, packageId: Int, paymentType: Int, lat: Double? = nil, lng: Double? = nil, streetAddress: String? = nil, timeslotId: String? = nil, inspectionDate: String? = nil, discountCodeId: Int?  = nil, walletBalance: Double, useWallet: Bool) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "AdId": adId,
                "PackageId": packageId,
                "PaymentType": paymentType,
                "WalletBalance": walletBalance,
                "UseWallet": useWallet
            ]
            
            if let lat = lat {
                data["Lat"] = lat
            }
            
            if let lng = lng {
                data["Lng"] = lng
            }
            
            if let streetAddress = streetAddress {
                data["StreetAddress"] = streetAddress
            }
            
            if let timeslotId = timeslotId {
                data["TimeslotId"] = timeslotId
            }
            
            if let inspectionDate = inspectionDate {
                data["InspectionDate"] = inspectionDate
            }
            
            if let discountCodeId = discountCodeId {
                data["DiscountCodeId"] = discountCodeId
            }
            
            self.getConnection().sendData(urlName: "InstantCashOffer/RepostCar", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func upgradeCarPackage(adId: Int, packageId: Int, paymentType: Int, lat: Double? = nil, lng: Double? = nil, streetAddress: String? = nil, timeslotId: String? = nil, inspectionDate: String? = nil, discountCodeId: Int? = nil, walletBalance: Double, useWallet: Bool) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "AdId": adId,
                "PackageId": packageId,
                "PaymentType": paymentType,
                "Platform": 1,
                "DeviceName": Device.current.description,
                "DeviceToken": SharedHelper.shared.getFromDefault(key: "deviceToken"),
                "DeviceID": UIDevice.current.identifierForVendor?.uuidString ?? "",
                "WalletBalance": walletBalance,
                "UseWallet": useWallet
            ]
            
            if let lat = lat {
                data["Lat"] = lat
            }
            
            if let lng = lng {
                data["Lng"] = lng
            }
            
            if let streetAddress = streetAddress {
                data["StreetAddress"] = streetAddress
            }
            
            if let timeslotId = timeslotId {
                data["TimeslotId"] = timeslotId
            }
            
            if let inspectionDate = inspectionDate {
                data["InspectionDate"] = inspectionDate
            }
            
            if let discountCodeId = discountCodeId {
                data["DiscountCodeId"] = discountCodeId
            }
            
            self.getConnection().sendData(urlName: "InstantCashOffer/UpgradeCarPackage", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlaceGetHomeLists(stepType: Int) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "Platform": 1,
                "DeviceName": Device.current.description,
                "DeviceToken": SharedHelper.shared.getFromDefault(key: "deviceToken"),
                "DeviceID": UIDevice.current.identifierForVendor?.uuidString ?? "",
                "StepType": stepType
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/GetHomeLists", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlaceGetAreaByLatLong(lat: Double, lng: Double) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "Platform": 1,
                "DeviceName": Device.current.description,
                "DeviceToken": SharedHelper.shared.getFromDefault(key: "deviceToken"),
                "DeviceID": UIDevice.current.identifierForVendor?.uuidString ?? "",
                "Lat": lat,
                "Lng": lng
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/GetAreaByLatLong", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlaceCreateUserAddress(lat: Double, lng: Double, street: String, landmark: String, blockNumber: String, fullAddress: String, name: String, id: Int? = nil) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "Platform": 1,
                "DeviceName": Device.current.description,
                "DeviceToken": SharedHelper.shared.getFromDefault(key: "deviceToken"),
                "DeviceID": UIDevice.current.identifierForVendor?.uuidString ?? "",
                "Lat": lat,
                "Lng": lng,
                "Street": street,
                "Landmark": landmark,
                "BlockNumber": blockNumber,
                "FullAddress": fullAddress,
                "Name": name
            ]
            
            if let id = id {
                data["Id"] = id
            }
            
            self.getConnection().sendData(urlName: "MarketPlace/CreateUserAddress", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlaceGetVendorsWithPackages(carTypeId: Int, serviceId: Int, userAddressId: Int) -> Observable<[VendorsWithPackages]?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "CarTypeId": carTypeId,
                "ServiceId": serviceId,
                "UserAddressId" : userAddressId
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/Vendors/List", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode([VendorsWithPackages].self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlaceGetSinglePackageDetails(vendorPackageId: Int, vendorId: Int, carTypeId: Int, serviceId: Int, userAddressId: Int) -> Observable<Packages?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "PackageId": vendorPackageId,
                "VendorId": vendorId,
                "CarTypeId": carTypeId,
                "ServiceId": serviceId,
                "UserAddressId": userAddressId,
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/Vendors/Packages/Details", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Packages.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlaceGetAvailableTimeSlots(carTypeId: Int, serviceId: Int, vendorId: Int, userAddressId: Int, areaId: Int, days: Int, startDate: String, packageId: Int) -> Observable<[TimeSlotsForDateRange]?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "Platform": 1,
                "CarTypeId": carTypeId,
                "ServiceId": serviceId,
                "VendorId": vendorId,
                "UserAddressId": userAddressId,
                "AreaId": areaId,
                "Days": days,
                "StartDate": startDate,
                "PackageId": packageId
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/GetAvailableTimeSlots", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode([TimeSlotsForDateRange].self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlacePostServiceRequestCarWash(carTypeId: Int, serviceId: Int, vendorId: Int, userAddressId: Int, paymentType: Int, packageId: Int, timeSlotId: String, scheduledDate: String, discountCodeId: Int? = nil, serviceRequestId: Int? = nil, addonIds: [Int]? = nil, walletBalance: Double, useWallet: Bool) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "Platform": 1,
                "CarTypeId": carTypeId,
                "ServiceId": serviceId,
                "VendorId": vendorId,
                "UserAddressId": userAddressId,
                "PaymentType": paymentType,
                "DeviceToken": SharedHelper.shared.getFromDefault(key: "deviceToken"),
                "DeviceID": UIDevice.current.identifierForVendor?.uuidString ?? "",
                "DeviceName": Device.current.description,
                "PackageId": packageId,
                "TimeSlotId": timeSlotId,
                "ScheduledDate": scheduledDate,
                "WalletBalance": walletBalance,
                "UseWallet": useWallet
            ]
            
            if let discountCodeId {
                data["DiscountCodeId"] = discountCodeId
            }
            
            if let serviceRequestId {
                data["ServiceRequestId"] = serviceRequestId
            }
            
            if let addonIds {
                data["AddonIds"] = addonIds
            }
            
            self.getConnection().sendData(urlName: "MarketPlace/PostServiceRequest", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func boostsPstRequestApi(id: Int, paymentType: Int, packageId: Int, discountCodeId: Int? = nil, useWallet: Bool, adId: Int) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "Platform": 1,
                "PaymentType": paymentType,
                "PackageId": packageId,
                "UseWallet": useWallet,
                "Id": id,
                "AdId": adId
            ]
            
            if let discountCodeId {
                data["DiscountCodeId"] = discountCodeId
            }
            
            self.getConnection().sendData(urlName: "Boost/Requests", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlaceGetVendorDetailsById(vendorId: Int, serviceId: Int, carTypeId: Int, userAddressId: Int) -> Observable<VendorsWithPackages?> {
        return Observable.create { observer in
            var data: [String: Any] = ["VendorId": vendorId, "ServiceId": serviceId, "CarTypeId": carTypeId, "UserAddressId": userAddressId]
            
            self.getConnection().sendData(urlName: "MarketPlace/GetVendorDetailsById", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(VendorsWithPackages.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlaceMarkFavouriteVendor(vendorId: Int) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "VendorId": vendorId,
                "Platform": 1,
                "DeviceName": Device.current.description
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/MarkFavouriteVendor", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlaceMarkPromoCodeValidate(vendorId: Int, code: String, serviceId: Int, packageId: Int, carTypeId: Int, userAddressId: Int) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "VendorId": vendorId,
                "Code": code,
                "ServiceId": serviceId,
                "PackageId": packageId,
                "CarTypeId": carTypeId,
                "UserAddressId": userAddressId
            ]
            
            self.getConnection().sendData(urlName: "DiscountCodes/validate", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func boostsPromoCodeValidate(code: String, packageId: Int) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = ["Code": code, "PackageId": packageId]
            
            self.getConnection().sendData(urlName: "DiscountCodes/boostPackage/validate", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlaceMarkGetMyDashboardCount() -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "Platform": 1,
                "DeviceToken": SharedHelper.shared.getFromDefault(key: "deviceToken"),
                "DeviceID": UIDevice.current.identifierForVendor?.uuidString ?? "",
                "DeviceName": Device.current.description
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/GetMyDashboardCount", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlaceMarkGetMyOrders() -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "Platform": 1,
                "DeviceToken": SharedHelper.shared.getFromDefault(key: "deviceToken"),
                "DeviceID": UIDevice.current.identifierForVendor?.uuidString ?? "",
                "DeviceName": Device.current.description
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/GetMyOrders", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlaceMarkGetMyOrderDetails(serviceRequestId: Int) -> Observable<MyOrderDetails?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "Platform": 1,
                "DeviceToken": SharedHelper.shared.getFromDefault(key: "deviceToken"),
                "DeviceID": UIDevice.current.identifierForVendor?.uuidString ?? "",
                "DeviceName": Device.current.description,
                "ServiceRequestId": serviceRequestId
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/GetMyOrderDetails", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(MyOrderDetails.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func sycPromoCodeValidate(code: String, estimatedPrice: Int, packageId: Int) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "Code": code,
                "EstimatedPrice": estimatedPrice,
                "packageId": packageId
            ]
            
            self.getConnection().sendData(urlName: "DiscountCodes/Syc/validate", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func upgradePackagePromoCodeValidate(code: String, estimatedPrice: Int, packageId: Int, adId: Int) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "Code": code,
                "EstimatedPrice": estimatedPrice,
                "packageId": packageId,
                "AdId": adId
            ]
            
            self.getConnection().sendData(urlName: "DiscountCodes/upgradePackage/validate", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlacePostServiceReview(description: String, stars: Int, lstServiceId: [Int], orderId: Int) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
            	"Platform": 1,
            	"DeviceName": Device.current.description,
                "Description": description,
                "Stars": stars,
                "LstServiceId": lstServiceId,
                "ReviewId": 0,
                "ServiceId": orderId
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/PostServiceReview", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlacePostServiceReviewLater() -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = ["Platform": 1, "DeviceName": Device.current.description]
            
            self.getConnection().sendData(urlName: "MarketPlace/PostServiceReviewLater", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func getMarketPlaceServicesDetailsById(serviceId: Int) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = ["ServiceId": serviceId]
            
            self.getConnection().sendData(urlName: "MarketPlace/GetServicesDetailsById", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func getMarketPlaceGetServices() -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "DeviceName": Device.current.description,
                "DeviceToken": SharedHelper.shared.getFromDefault(key: "deviceToken"),
                "DeviceID": UIDevice.current.identifierForVendor?.uuidString ?? "",
                "Platform": 1
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/GetServices", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlacePostCarInspection(
        paymentType: Int,
        packageId: Int,
        timeSlotId: String,
        inspectionDate: String,
        discountCodeId: Int? = nil,
        lat: Double,
        long: Double,
        cityId: Int,
        areaId: Int,
        brandId: Int,
        modelId: Int,
        year: Int,
        inspectionLocation: Int,
        sellerAddress: String,
        additionalDetails: String,
        homeServiceFee: Int,
        walletBalance: Int,
        useWallet: Bool
    ) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "DeviceToken": SharedHelper.shared.getFromDefault(key: "deviceToken"),
                "DeviceID": UIDevice.current.identifierForVendor?.uuidString ?? "",
                "DeviceName": Device.current.description,
                "Platform": 1,
                "PaymentType": paymentType,
                "PackageId": packageId,
                "TimeSlotId": timeSlotId,
                "InspectionDate": inspectionDate,
                "Lat": lat,
                "Long": long,
                "CityId": cityId,
                "AreaId": areaId,
                "BrandId": brandId,
                "ModelId": modelId,
                "Year": year,
                "InspectionLocation": inspectionLocation,
                "SellerAddress": sellerAddress,
                "AdditionalDetails": additionalDetails,
                "HomeServiceFee": homeServiceFee,
                "WalletBalance": walletBalance,
                "UseWallet": useWallet
            ]
            
            if let discountCodeId {
                data["DiscountCodeId"] = discountCodeId
            }
            
            self.getConnection().sendData(urlName: "InstantCashOffer/PostCarInspection", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func getMarketPlaceVendorListForWindshieldFlow(carTypeId: Int, serviceId: Int, userAddressId: Int) -> Observable<[WindshieldVendorsList]?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "Platform": 1,
                "CarTypeId": carTypeId,
                "ServiceId": serviceId,
                "UserAddressId": userAddressId
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/GetVendorsList", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode([WindshieldVendorsList].self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func getMarketPlaceGetVendorServiceDetails(serviceId: Int, vendorId: Int, carTypeId: Int) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "ServiceId": serviceId,
                "VendorId": vendorId,
                "CarTypeId": carTypeId
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/GetVendorServiceDetails", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlacePostWindShieldRequest(serviceId: Int, vendorId: Int, carTypeId: Int, userAddressId: Int, brandId: Int, modelId: Int, damagePhotos: [UploadImageItemModel], notes: String, year: Int) -> Observable<Result?> {
        return  Observable.create { observer in
            var headerData: [String: Any] = [:]
            
            headerData["ServiceId"] = serviceId
            headerData["VendorId"] = vendorId
            headerData["CarTypeId"] = carTypeId
            headerData["UserAddressId"] = userAddressId
            headerData["BrandId"] = brandId
            headerData["ModelId"] = modelId
            headerData["Year"] = year
            headerData["Notes"] = notes

            self.getConnection().uploadAd(urlName: "MarketPlace/PostWindShieldRequest", data: headerData, headers: self.getHeader(), uploadedImages: damagePhotos).bind { response in
                let json = JSON(response ?? [:])
                
                if response as? String != "fail" {
                    if let result = CodableHandler.decodeClass(Result.self, classJsonData: json) {
                        observer.onNext(result)
                    }
                } else {
                    if let result = CodableHandler.decodeClass(Result.self, classJsonData: json) {
                        observer.onNext(result)
                    }
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlaceGetFavouriteVendors() -> Observable<[FavouriteVendor]?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "Platform": 1,
                "DeviceToken": SharedHelper.shared.getFromDefault(key: "deviceToken"),
                "DeviceID": UIDevice.current.identifierForVendor?.uuidString ?? "",
                "DeviceName": Device.current.description
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/GetFavouriteVendors", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode([FavouriteVendor].self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func marketPlaceGetVendorReviews(vendorId: Int, page: Int, pageSize: Int) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "VendorId": vendorId,
                "Page": page,
                "PageSize": pageSize,
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/vendors/reviews", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func getAddressesByUser() -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "Platform": 1,
                "DeviceToken": SharedHelper.shared.getFromDefault(key: "deviceToken"),
                "DeviceID": UIDevice.current.identifierForVendor?.uuidString ?? "",
                "DeviceName": Device.current.description
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/GetAddressesByUser", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func deleteUserAddress(addressId: Int) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = [
                "Platform": 1,
                "DeviceToken": SharedHelper.shared.getFromDefault(key: "deviceToken"),
                "DeviceID": UIDevice.current.identifierForVendor?.uuidString ?? "",
                "DeviceName": Device.current.description,
                "AddressId": addressId
            ]
            
            self.getConnection().sendData(urlName: "MarketPlace/DeleteUserAddress", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func getFavouriteVendorProfile(vendorId: Int) -> Observable<Result?> {
        return Observable.create { observer in
            var data: [String: Any] = ["VendorId": vendorId]
            
            self.getConnection().sendData(urlName: "MarketPlace/GetVendorProfile", data: data, headers: self.getHeader()).bind { response in
                if response as? String != "fail" {
                    let jsonResponse = JSON(response ?? [:])
                    if let result = CodableHandler.decode(Result.self, from: jsonResponse) {
                        observer.onNext(result)
                    } else {
                        observer.onNext(nil)
                    }
                } else {
                    observer.onNext(nil)
                }
            }
            return Disposables.create()
        }
    }
    
    public func getPackageBundlesDetails(isBundle: Bool, includeSelfService: Bool, completion: @escaping ([PackageBundelModel]?) -> Void) {
        let data: [String: Any] = ["IsBundle": isBundle, "IncludeSelfService": includeSelfService]
        
        self.getConnection().sendDataURLSession(urlName: "InstantCashOffer/GetPackageDetails", data: data, headers: self.getHeader()) { response in
            guard response as? String != "fail" else {
                completion(nil)
                return
            }
            let jsonResponse = JSON(response ?? [:])
            let result = CodableHandler.decode([PackageBundelModel].self, from: jsonResponse)
            completion(result)
        }
    }
    
    public func getBoostPackages(completion: @escaping (BoostPackagesModel?) -> Void) {
        self.getConnection().sendDataURLSession(urlName: "Boost/Packages", headers: self.getHeader()) { response in
            guard response as? String != "fail" else {
                completion(nil)
                return
            }
            let jsonResponse = JSON(response ?? [:])
            let result = CodableHandler.decode(BoostPackagesModel.self, from: jsonResponse)
            completion(result)
        }
    }
    
    public func buyBundleMicroDealerFlow(useWallet: Bool, packageId: Int, boostPackageId: Int, discountCodeId: Int? = nil, paymentType: Int, completion: @escaping (Result?) -> Void) {
        var data: [String: Any] = [
            "BoostPackageId": boostPackageId,
            "PackageId": packageId,
            "UseWallet": useWallet,
            "PaymentType": paymentType
        ]
        
        if let discountCodeId {
            data["DiscountCodeId"] = discountCodeId
        }

        self.getConnection().sendDataURLSession(urlName: "MicroDealerSubscriptions/CreateSubscription", data: data, headers: self.getHeader()) { response in
            guard response as? String != "fail" else {
                completion(nil)
                return
            }
            let jsonResponse = JSON(response ?? [:])
            let result = CodableHandler.decode(Result.self, from: jsonResponse)
            completion(result)
        }
    }
    
    public func sycPromoCodeValidateForMicroDealerBuyBundleFlow(code: String, estimatedPrice: Int, packageId: Int, completion: @escaping (Result?) -> Void) {
        var data: [String: Any] = [
            "Code": code,
            "EstimatedPrice": estimatedPrice,
            "packageId": packageId
        ]
        
        self.getConnection().sendDataURLSession(urlName: "DiscountCodes/Syc/validate", data: data, headers: self.getHeader()) { response in
            guard response as? String != "fail" else {
                completion(nil)
                return
            }
            let jsonResponse = JSON(response ?? [:])
            let result = CodableHandler.decode(Result.self, from: jsonResponse)
            completion(result)
        }
    }
}
